<script setup lang="ts">
import { computed, ref, useTemplateRef, watch } from 'vue';
import { deepClone, renderLabel } from '@/script';
import type { AnyIndicatorRiskParamObject, CommonRiskAlertConfig } from '@/types/riskc';
import { IdcComponentNameDef } from '../../ComponentNameDef';

import {
  AlertType,
  AlertTypes,
  ExpressionType,
  ExpressionTypes,
  PriceDeviationType,
  PriceDeviationTypes,
} from '@/enum/riskc';

/**
 * 价格偏离度风控参数
 */
interface RuleInnerSetting extends AnyIndicatorRiskParamObject {
  deviationType: number;
  paramAlert: CommonRiskAlertConfig;
  paramBlock: CommonRiskAlertConfig;
}

const $form = useTemplateRef('$form');
const localRuleSetting = ref<RuleInnerSetting>(createEmptyRiskParam());

const definedActions = computed(() => {
  const { paramAlert, paramBlock } = localRuleSetting.value as RuleInnerSetting;
  return [paramAlert, paramBlock];
});

const { ruleSetting } = defineProps<{ ruleSetting: RuleInnerSetting | null }>();

const rules = {
  deviationType: [{ required: true, message: '请选择比较符', trigger: 'blur' }],
};

watch(
  () => ruleSetting,
  newValue => {
    localRuleSetting.value = newValue ? deepClone(newValue) : createEmptyRiskParam();
  },
  { immediate: true },
);

function createEmptyRiskParam(): RuleInnerSetting {
  return {
    classType: IdcComponentNameDef.PriceDeviation,
    deviationType: PriceDeviationType.LatestPrice.value,
    paramAlert: {
      expression: ExpressionType.GreaterThan.value,
      value: 2,
      alertType: AlertType.Warning.value,
    },
    paramBlock: {
      expression: ExpressionType.GreaterThan.value,
      value: 5,
      alertType: AlertType.Ignore.value,
    },
  };
}

function validate() {
  return $form.value!.validate();
}

const emitter = defineEmits<{
  riskParamChanged: [descrption: string];
}>();

function handleParamHotChange() {
  emitter('riskParamChanged', getRiskParamSummary());
}

function getRiskParamSummary() {
  const { deviationType, paramAlert, paramBlock } = localRuleSetting.value;
  return `${renderLabel(deviationType, PriceDeviationTypes)}
  「${paramAlert.expression}${paramAlert.value}万元时」${renderLabel(paramAlert.alertType, AlertTypes)}，
  「${paramBlock.expression}${paramBlock.value}万元时」${renderLabel(paramBlock.alertType, AlertTypes)}；`;
}

function getRiskSetting() {
  return deepClone(localRuleSetting.value);
}

defineExpose({
  validate,
  getRiskSetting,
  getRiskParamSummary,
});
</script>

<template>
  <div class="rule-tmpl" h-full pr-12>
    <el-form ref="$form" :model="localRuleSetting" :rules="rules" label-width="80px">
      <div class="custom-row">
        <el-form-item label="指标设置" prop="deviationType">
          <div w-full flex aic gap-10>
            <div w-auto>
              <el-select
                v-model="localRuleSetting.deviationType"
                style="width: 200px"
                @change="handleParamHotChange"
              >
                <el-option
                  v-for="(item, idx) in PriceDeviationTypes"
                  :key="idx"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </div>
        </el-form-item>
      </div>
      <template v-for="(item, idx) in definedActions" :key="idx">
        <div class="custom-row">
          <el-form-item label="" prop="expression">
            <div w-full flex aic gap-10>
              <el-select
                v-model="item.expression"
                style="width: 100px"
                @change="handleParamHotChange"
              >
                <el-option
                  v-for="(item, idx) in ExpressionTypes"
                  :key="idx"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <el-input-number
                :controls="false"
                :precision="0"
                :step="1"
                :min="0"
                :max="100"
                v-model="item.value"
                @change="handleParamHotChange"
                style="width: 100px"
              ></el-input-number>
              <label class="placed-label">%时</label>
              <el-select
                v-model="item.alertType"
                style="width: 140px"
                @change="handleParamHotChange"
              >
                <el-option
                  v-for="(item, idx) in AlertTypes"
                  :key="idx"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </el-form-item>
        </div>
      </template>
    </el-form>
  </div>
</template>

<style scoped>
.rule-tmpl {
  .placed-label {
    color: var(--g-text-color-2);
  }

  :deep() {
    .el-form-item__label {
      color: var(--g-text-color-2);
    }
  }

  .custom-row {
    margin-bottom: 18px;

    :deep() {
      > .el-form-item {
        margin-bottom: 0 !important;
      }
    }
  }

  .post-item {
    :deep() {
      > .el-form-item__content {
        margin-left: 0 !important;
      }
    }
  }
}
</style>
