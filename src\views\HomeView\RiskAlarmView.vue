<script setup lang="tsx">
import { ref, onMounted } from 'vue';
import { IdentityTypes, Repos, type RiskMessage } from '../../../../xtrade-sdk/dist';
import { TableV2SortOrder } from 'element-plus';
import VirtualizedTable from '@/components/common/VirtualizedTable.vue';
import type { ColumnDefinition } from '@/types';
import { formatDateTime, renderLabel } from '@/script/formatter';

interface CellRenderParam {
  rowData: RiskMessage;
  cellData: any;
}

const repoInstance = new Repos.RiskControlRepo();
const records = ref<RiskMessage[]>([]);

const columns: ColumnDefinition<RiskMessage> = [
  { key: 'id', title: 'ID', width: 60 },
  { key: 'riskRuleId', title: '风控规则ID', width: 100 },
  { key: 'warningType', title: '预警类型', width: 100, sortable: true },
  {
    key: 'identityType',
    title: '报警对象类型',
    width: 150,
    sortable: true,
    cellRenderer: (params: CellRenderParam) => {
      return <span>{renderLabel(params.cellData, IdentityTypes)}</span>;
    },
  },
  { key: 'identityName', title: '报警对象名称', width: 200, sortable: true },
  { key: 'identity', title: '报警对象ID', width: 150, sortable: true },
  { key: 'content', title: '消息内容', width: 400, sortable: true },
  { key: 'tradingDay', title: '交易日', width: 100, sortable: true },
  {
    key: 'createTime',
    title: '时间',
    width: 150,
    sortable: true,
    cellRenderer: (params: CellRenderParam) => {
      return <span>{formatDateTime(params.cellData)}</span>;
    },
  },
];

async function request() {
  records.value = (await repoInstance.QueryRiskMessages()).data || [];
}

onMounted(() => {
  request();
});
</script>

<template>
  <div class="risk-alarm-view">
    <VirtualizedTable
      :sort="{ key: 'createTime', order: TableV2SortOrder.DESC }"
      :columns="columns"
      :data="records"
    >
      <template #actions></template>
    </VirtualizedTable>
  </div>
</template>

<style scoped>
.risk-alarm-view {
  height: 100%;
  padding: 10px;
}
</style>
