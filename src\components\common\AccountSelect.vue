<template>
  <el-select
    :model-value="modelValue"
    @update:model-value="handleChange"
    placeholder="请选择账号"
    filterable
    clearable
    :loading="loading"
  >
    <el-option
      v-for="account in filteredAccounts"
      :key="account.accountId"
      :label="`${account.accountName} (${account.financeAccountName})`"
      :value="account.accountId"
    />
    <template #footer>
      <div p-2>
        <el-button type="primary" size="small" w-full @click="handleAddAccount">
          <i class="iconfont icon-add" mr-1></i>
          添加账号
        </el-button>
      </div>
    </template>
  </el-select>
</template>

<script setup lang="ts">
import { computed, ref, shallowRef, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Repos, type SimpleAccountInfo, type LegacyAccountInfo } from '../../../../xtrade-sdk/dist';
import { getUser } from '@/script';

interface Props {
  modelValue?: string;
  orgId?: number;
  excludeIds?: string[];
  useUnboundOnly?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  excludeIds: () => [],
  useUnboundOnly: false,
});

const emit = defineEmits<{
  'update:modelValue': [value: string | undefined];
  accountCreated: [account: LegacyAccountInfo];
  addAccount: [orgId?: number];
}>();

// 响应式数据
const loading = ref(false);
const allAccounts = shallowRef<SimpleAccountInfo[]>([]);

// 仓库实例
const governanceRepo = new Repos.GovernanceRepo();
const currentUser = getUser()!;

// 计算属性：过滤后的账号列表
const filteredAccounts = computed(() => {
  let accounts = allAccounts.value;

  // 排除指定的账号ID
  if (props.excludeIds.length > 0) {
    accounts = accounts.filter(a => !props.excludeIds.includes(a.accountId));
  }

  return accounts;
});

// 处理选择变化
const handleChange = (value: string | undefined) => {
  emit('update:modelValue', value);
};

// 加载账号列表
const loadAccounts = async () => {
  if (!props.orgId) return;

  loading.value = true;
  try {
    if (props.useUnboundOnly) {
      // 使用QueryUnboundAccounts接口获取未绑定的账号
      const { errorCode, errorMsg, data } = await governanceRepo.QueryUnboundAccounts(props.orgId);
      if (errorCode === 0) {
        allAccounts.value = data || [];
      } else {
        ElMessage.error(errorMsg || '加载账号列表失败');
      }
    } else {
      // 使用QueryAccounts接口获取所有账号，然后按机构过滤
      const { errorCode, errorMsg, data } = await governanceRepo.QueryAccounts();
      if (errorCode === 0) {
        allAccounts.value = (data || [])
          .filter(account => account.orgId === props.orgId)
          .map(
            account =>
              ({
                accountId: account.id,
                accountName: account.accountName,
                financeAccountName: account.financeAccount,
                assetType: account.assetType,
              }) as SimpleAccountInfo,
          );
      } else {
        ElMessage.error(errorMsg || '加载账号列表失败');
      }
    }
  } catch (error) {
    ElMessage.error('加载账号列表失败');
  } finally {
    loading.value = false;
  }
};

// 处理添加账号
const handleAddAccount = () => {
  emit('addAccount', props.orgId);
};

// 外部调用的方法，用于添加新账号到列表
const addAccount = (account: LegacyAccountInfo) => {
  const newAccount: SimpleAccountInfo = {
    accountId: account.id,
    accountName: account.accountName,
    financeAccountName: account.financeAccount,
    assetType: account.assetType,
  };

  allAccounts.value.push(newAccount);

  // 如果新建的账号符合当前过滤条件，自动选择
  const shouldAutoSelect = !props.orgId || account.orgId === props.orgId;
  if (shouldAutoSelect) {
    emit('update:modelValue', account.id);
  }

  emit('accountCreated', account);
};

// 暴露方法给父组件
defineExpose({
  addAccount,
});

// 监听orgId变化，重新加载账号
watch(
  () => props.orgId,
  newOrgId => {
    if (newOrgId) {
      loadAccounts();
    } else {
      allAccounts.value = [];
    }
  },
  { immediate: true },
);

// 监听orgId变化，清空当前选择
watch(
  () => props.orgId,
  () => {
    if (props.modelValue) {
      const isCurrentAccountValid = filteredAccounts.value.some(
        a => a.accountId === props.modelValue,
      );
      if (!isCurrentAccountValid) {
        emit('update:modelValue', undefined);
      }
    }
  },
);
</script>

<style scoped>
:deep(.el-select-dropdown__footer) {
  padding: 0;
}
</style>
