<template>
  <div>
    <el-select
      ref="selectRef"
      v-model="model"
      placeholder="请选择产品"
      filterable
      clearable
      :loading="loading"
      @change="handleChange"
    >
      <el-option
        v-for="product in filteredProducts"
        :key="product.id"
        :label="product.fundName"
        :value="product.id"
      />
      <template v-if="canCreate" #footer>
        <div p-2>
          <el-button type="primary" size="small" w-full @click="handleAddProduct">
            <i class="iconfont icon-add" mr-1></i>
            添加产品
          </el-button>
        </div>
      </template>
    </el-select>
    <!-- 新建产品对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="新建产品"
      width="800px"
      draggable
      class="typical-dialog"
      destroy-on-close
      append-to-body
    >
      <ProductBasicInfoForm
        ref="createProductFormRef"
        v-model="contextProduct"
        v-if="showCreateDialog"
        :real="real"
        :org-id="orgId"
        @save="handleCreateProductSaved"
        @cancel="showCreateDialog = false"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {
  computed,
  onMounted,
  ref,
  shallowRef,
  watch,
  defineAsyncComponent,
  useTemplateRef,
} from 'vue';
import { ElMessage } from 'element-plus';
import { Repos } from '../../../../xtrade-sdk/dist';
import { type ProductInfo } from '@/types';
import { getUser, hasPermission } from '@/script';
import { FundTypeEnum, MenuPermitUserManagement } from '@/enum';
const ProductBasicInfoForm = defineAsyncComponent(
  () => import('../ProductView/ProductBasicInfoForm.vue'),
);

interface Props {
  /** 机构ID，用于按机构过滤产品列表 */
  orgId?: number;
  /** 需要排除的产品ID列表 */
  excludeIds?: string[];
  /** 选择关联产品时为true, 只能选择真实产品 */
  real?: boolean;
  /** 来自哪个菜单路由，用于权限判断 */
  from?: string;
  /** 是否按用户所属机构过滤产品列表 */
  filterByUserOrg?: boolean;
}

const { filterByUserOrg = false, excludeIds = [], orgId, real, from } = defineProps<Props>();

const emit = defineEmits<{
  change: [value: string | undefined, product?: ProductInfo];
  save: [product: ProductInfo];
  refresh: [];
}>();

const selectRef = useTemplateRef('selectRef');
const createProductFormRef = useTemplateRef('createProductFormRef');

const contextProduct = ref();
const model = defineModel<string | undefined>();

// 响应式数据
const loading = ref(false);
const allProducts = shallowRef<ProductInfo[]>([]);
const showCreateDialog = ref(false);

const canCreate = computed(() => {
  // 产品表单选择关联产品时，肯定有权限
  if (real) return true;
  // 用户管理选择分享产品
  else if (from == 'user') {
    return hasPermission(MenuPermitUserManagement.创建产品);
  } else {
    console.warn('ProductSelect: 未设置创建权限判断依据');
    return false;
  }
});

// 仓库实例
const governanceRepo = new Repos.GovernanceRepo();
const currentUser = getUser()!;

// 计算属性：过滤后的产品列表
const filteredProducts = computed(() => {
  let products = allProducts.value;

  // 按机构过滤
  if (orgId) {
    products = products.filter(p => p.orgId === orgId);
  } else if (filterByUserOrg) {
    products = products.filter(p => p.orgId === currentUser.orgId);
  }

  // 排除指定的产品ID
  if (excludeIds.length > 0) {
    products = products.filter(p => !excludeIds.includes(p.id));
  }

  // 只显示真实产品
  if (real) {
    products = products.filter(p => p.fundType === FundTypeEnum.产品);
  }

  return products;
});

// 监听orgId变化，重新过滤产品
watch(
  () => orgId,
  () => {
    // 如果当前选择的产品不在新的过滤结果中，清空选择
    if (model.value) {
      const isCurrentProductValid = filteredProducts.value.some(p => p.id === model.value);
      if (!isCurrentProductValid) {
        model.value = undefined;
      }
    }
  },
);

onMounted(() => {
  loadProducts();
});

// 处理选择变化
const handleChange = (value: string | undefined) => {
  emit(
    'change',
    value,
    allProducts.value.find(p => p.id === value),
  );
  selectRef.value?.blur();
};

// 加载产品列表
const loadProducts = async () => {
  loading.value = true;
  const { errorCode, errorMsg, data } = await governanceRepo.QueryProducts();
  if (errorCode === 0) {
    allProducts.value = data || [];
  } else {
    ElMessage.error(errorMsg || '加载产品列表失败');
  }
  loading.value = false;
};

// 处理添加产品
const handleAddProduct = () => {
  selectRef.value?.blur();
  showCreateDialog.value = true;
};

const handleCreateProductSaved = async (product: ProductInfo) => {
  showCreateDialog.value = false;
  await loadProducts();
  model.value = String(product.id);
  emit('refresh');
  selectRef.value?.blur();
};
</script>

<style scoped></style>
