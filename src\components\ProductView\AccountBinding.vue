<script setup lang="tsx">
import { ref, computed, shallowRef, watch, onMounted, useTemplateRef } from 'vue';
import { ElMessage } from 'element-plus';
import {
  AssetType,
  Repos,
  type LegacyAccountInfo,
  type SimpleAccountInfo,
} from '../../../../xtrade-sdk/dist';
import VirtualizedTable from '../common/VirtualizedTable.vue';
import AccountSelect from '../common/AccountSelect.vue';
import type { ColumnDefinition, RowAction, ProductInfo } from '@/types';
import { renderLabelCol, thousands, thousandsCol } from '@/script';

const contextProduct = defineModel<ProductInfo | null>({});

// 仓库实例
const governanceRepo = new Repos.GovernanceRepo();

const allAccounts = shallowRef<LegacyAccountInfo[]>([]);
const availableAccounts = shallowRef<SimpleAccountInfo[]>([]);
const selectedAccountForBind = ref<string>('');
const accountSelectRef = useTemplateRef('accountSelectRef');

// 已绑定账号
const boundAccounts = computed(() => {
  if (!contextProduct.value) return [];
  return allAccounts.value.filter(x =>
    contextProduct.value!.accounts.some(y => y.accountId == x.id),
  );
});

// 已绑定账号表格列定义
const boundAccountColumns: ColumnDefinition<LegacyAccountInfo> = [
  {
    key: 'accountName',
    title: '账号',
    width: 180,
    sortable: true,
    fixed: true,
  },
  {
    key: 'assetType',
    title: '类型',
    width: 60,
    sortable: true,
    cellRenderer: params => renderLabelCol(params, Object.values(AssetType)),
  },
  {
    key: 'orgName',
    title: '机构',
    width: 120,
    sortable: true,
  },
  {
    key: 'brokerName',
    title: '经纪商',
    width: 120,
    sortable: true,
  },
  {
    key: 'balance',
    title: '权益',
    width: 150,
    sortable: true,
    align: 'right',
    cellRenderer: thousandsCol,
    textRenderer: thousands,
  },
  {
    key: 'marketValue',
    title: '市值',
    width: 130,
    sortable: true,
    align: 'right',
    cellRenderer: thousandsCol,
    textRenderer: thousands,
  },
  {
    key: 'available',
    title: '可用资金',
    width: 150,
    sortable: true,
    align: 'right',
    cellRenderer: thousandsCol,
    textRenderer: thousands,
  },
  { key: 'accountId', title: 'ID', width: 150, sortable: true },
];

// 已绑定账号行操作
const boundAccountRowActions: RowAction<LegacyAccountInfo>[] = [
  {
    label: '解除绑定',
    type: 'danger',
    onClick: (row: LegacyAccountInfo) => {
      handleUnbindAccount(row);
    },
  },
];

// 加载所有账号列表
const loadAllAccounts = async () => {
  const { errorCode, errorMsg, data } = await governanceRepo.QueryAccounts();
  if (errorCode === 0) {
    allAccounts.value = data || [];
  } else {
    ElMessage.error(errorMsg || '加载账号列表失败');
  }
};

// 加载可用账号列表（保留用于其他地方可能的调用）
const loadAvailableAccounts = async () => {
  if (!contextProduct.value) return;
  const { errorCode, errorMsg, data } = await governanceRepo.QueryUnboundAccounts(
    contextProduct.value.orgId,
  );
  if (errorCode === 0) {
    availableAccounts.value = data || [];
  } else {
    ElMessage.error(errorMsg || '加载账号列表失败');
  }
};

// 处理账号选择变化
const handleAccountChange = (accountId: string | undefined) => {
  if (accountId) {
    handleBindAccount(accountId);
  }
};

// 绑定账号到产品
const handleBindAccount = async (accountId: string) => {
  const account = allAccounts.value.find(item => item.id == accountId)!;
  const { errorCode, errorMsg } = await governanceRepo.BindProductAccounts(
    contextProduct.value!.id,
    contextProduct.value!.accounts.map(x => x.accountId as string).concat(accountId),
  );
  if (errorCode === 0) {
    ElMessage.success('绑定成功');
    contextProduct.value!.accounts.push({
      accountId: account.id,
      accountName: account.accountName,
      assetType: account.assetType,
    } as any);
    // AccountSelect组件会自动重新加载
  } else {
    ElMessage.error(errorMsg || '绑定失败');
  }
  // 清空选择
  selectedAccountForBind.value = '';
};

// 处理添加账号
const handleAddAccount = (orgId?: number) => {
  // 这里可以触发全局的账号创建对话框
  // 或者通知父组件处理
  ElMessage.info('请通过账号管理页面创建新账号');
};

// 处理账号创建成功
const handleAccountCreated = (account: LegacyAccountInfo) => {
  allAccounts.value.push(account);
  ElMessage.success('账号创建成功');
};

// 解除账号绑定
const handleUnbindAccount = async (account: LegacyAccountInfo) => {
  const { errorCode, errorMsg } = await governanceRepo.BindProductAccounts(
    contextProduct.value!.id,
    contextProduct
      .value!.accounts.filter(x => x.accountId != account.id)
      .map(x => x.accountId as string),
  );
  if (errorCode === 0) {
    ElMessage.success('解绑成功');
    contextProduct.value!.accounts = contextProduct.value!.accounts.filter(
      x => x.accountId != account.id,
    );
    // AccountSelect组件会自动重新加载
  } else {
    ElMessage.error(errorMsg || '解绑失败');
  }
};

// 监听产品变化（AccountSelect组件会自动处理账号加载）
watch(
  () => contextProduct.value?.id,
  () => {
    // AccountSelect组件会自动根据orgId变化重新加载账号
  },
  { immediate: true },
);

onMounted(() => {
  loadAllAccounts();
});
</script>

<template>
  <div class="account-binding-view product-guide-slide" pr-10 flex flex-col>
    <!-- 账号选择和绑定 -->
    <div w-200 mt-10>
      <AccountSelect
        ref="accountSelectRef"
        v-model="selectedAccountForBind"
        :org-id="contextProduct?.orgId"
        use-unbound-only
        @update:model-value="handleAccountChange"
        @add-account="handleAddAccount"
        @account-created="handleAccountCreated"
      />
    </div>

    <!-- 已绑定账号列表 -->
    <div class="bound-accounts-section" mb-6>
      <h3 class="section-title" mb-3>已绑定账号</h3>
      <div h-400>
        <VirtualizedTable
          :columns="boundAccountColumns"
          :data="boundAccounts"
          :row-actions="boundAccountRowActions"
          :row-action-width="120"
          :show-toolbar="false"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.account-name-cell {
  display: flex;
  flex-direction: column;
}

.account-name {
  font-weight: 500;
}

.finance-account {
  font-size: 12px;
  opacity: 0.7;
}
</style>
