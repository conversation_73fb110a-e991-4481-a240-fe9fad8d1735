/**
 * TICK行情类型
 */
export enum TickType {
  /** 简单TICK */
  simple = 4,
  /** 标准TICK */
  tick = 5,
  /** K线TICK */
  kline = 20,
  /** 成交TICK */
  transaction = 60,
  /** 订单队列TICK */
  queue = 70,
  /** 预埋单TICK */
  preplaced = 71,
  /** 前序排单TICK */
  front = 72,
  /** 前序撤单TICK */
  frontCancel = 73,
}

/**
 * 主体类型
 */
export enum IdentityType {
  /** 全局 */
  global = 0,
  /** 账号 */
  account = 1,
  /** 策略 */
  strategy = 2,
  /** 产品 */
  fund = 3,
  /** 风控组 */
  group = 10,
}

/**
 * 主体类型
 */
export const IdentityTypes = [
  { label: '全局', value: IdentityType.global },
  { label: '账号', value: IdentityType.account },
  { label: '策略', value: IdentityType.strategy },
  { label: '产品', value: IdentityType.fund },
  { label: '风控组', value: IdentityType.group },
];
