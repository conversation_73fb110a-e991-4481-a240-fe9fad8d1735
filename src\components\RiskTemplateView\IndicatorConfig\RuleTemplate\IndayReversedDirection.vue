<script setup lang="ts">
import { ref, useTemplateRef, watch } from 'vue';
import { deepClone, renderLabel } from '@/script';
import type { AnyIndicatorRiskParamObject } from '@/types/riskc';
import { AlertType, AlertTypes } from '@/enum/riskc';
import { IdcComponentNameDef } from '../../ComponentNameDef';

/**
 * 日内反向
 */
interface RuleInnerSetting extends AnyIndicatorRiskParamObject {
  /** 日内反向 */
  intraDayReversalAlert: number;
}

const $form = useTemplateRef('$form');
const localRuleSetting = ref<RuleInnerSetting>(createEmptyRiskParam());

const { ruleSetting } = defineProps<{ ruleSetting: RuleInnerSetting | null }>();

const rules = {
  intraDayReversalAlert: [{ required: true, message: '请选择', trigger: 'blur' }],
};

watch(
  () => ruleSetting,
  newValue => {
    localRuleSetting.value = newValue ? deepClone(newValue) : createEmptyRiskParam();
  },
  { immediate: true },
);

function createEmptyRiskParam(): RuleInnerSetting {
  return {
    classType: IdcComponentNameDef.SelfTrade,
    intraDayReversalAlert: AlertType.Warning.value,
  };
}

function validate() {
  return $form.value!.validate();
}

const emitter = defineEmits<{
  riskParamChanged: [descrption: string];
}>();

function handleParamHotChange() {
  emitter('riskParamChanged', getRiskParamSummary());
}

function getRiskParamSummary() {
  const { intraDayReversalAlert } = localRuleSetting.value;
  return `日内反向「${renderLabel(intraDayReversalAlert, AlertTypes)}」；`;
}

function getRiskSetting() {
  return deepClone(localRuleSetting.value);
}

defineExpose({
  validate,
  getRiskSetting,
  getRiskParamSummary,
});
</script>

<template>
  <div class="rule-tmpl" h-full pr-12>
    <el-form ref="$form" :model="localRuleSetting" :rules="rules" label-width="80px">
      <div class="custom-row">
        <el-form-item label="指标设置" prop="intraDayReversalAlert">
          <span pr-5>日内反向</span>
          <el-select
            v-model="localRuleSetting.intraDayReversalAlert"
            style="width: 100px"
            @change="handleParamHotChange"
          >
            <el-option
              v-for="(item, idx) in AlertTypes"
              :key="idx"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<style scoped>
.rule-tmpl {
  .placed-label {
    color: var(--g-text-color-2);
  }

  :deep() {
    .el-form-item__label {
      color: var(--g-text-color-2);
    }
  }

  .custom-row {
    margin-bottom: 18px;

    :deep() {
      > .el-form-item {
        margin-bottom: 0 !important;
      }
    }
  }

  .post-item {
    :deep() {
      > .el-form-item__content {
        margin-left: 0 !important;
      }
    }
  }
}
</style>
